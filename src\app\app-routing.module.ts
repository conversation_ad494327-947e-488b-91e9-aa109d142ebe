import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { Roles } from './sharedModels';

// Guards
import { LoginGuardService, AuthGuardService } from './sharedServices';

const routes: Routes = [
  {
    path: '',
    loadChildren: () => import('./authentication/authentication.module').then(m => m.AuthenticationModule),
    canActivate: [LoginGuardService],
  },
  {
    path: 'admin',
    loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule),
    canActivate: [AuthGuardService],
    data: { role: Roles.Admin },
  },
  {
    path: 'canteen',
    loadChildren: () => import('./canteen/canteen.module').then(m => m.CanteenModule),
    canActivate: [AuthGuardService],
    data: { role: Roles.Canteen },
  },
  {
    path: 'family',
    loadChildren: () => import('./family/family.module').then(m => m.FamilyModule),
    canActivate: [AuthGuardService],
    data: { role: Roles.Parent },
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      scrollPositionRestoration: 'top',
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
