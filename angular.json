{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"cashless": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/cashless", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.json"], "styles": ["src/styles.scss"], "scripts": [], "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "9mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}], "serviceWorker": true, "ngswConfigPath": "src/ngsw-config.json"}, "uat": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.uat.ts"}], "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "9mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}], "serviceWorker": true, "ngswConfigPath": "src/ngsw-config.json"}, "qa": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.qa.ts"}], "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "9mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}], "serviceWorker": true, "ngswConfigPath": "src/ngsw-config.json"}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "9mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}], "serviceWorker": false, "ngswConfigPath": "src/ngsw-config.json"}, "debug": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.debug.ts"}], "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}], "serviceWorker": false, "ngswConfigPath": "src/ngsw-config.json"}, "preprod": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.preprod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}], "serviceWorker": false, "ngswConfigPath": "src/ngsw-config.json"}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "cashless:build"}, "configurations": {"production": {"browserTarget": "cashless:build:production"}, "uat": {"browserTarget": "cashless:build:uat"}, "qa": {"browserTarget": "cashless:build:qa"}, "dev": {"browserTarget": "cashless:build:dev"}, "debug": {"browserTarget": "cashless:build:debug"}, "preprod": {"browserTarget": "cashless:build:preprod"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "cashless:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.scss"], "scripts": [], "assets": ["src/favicon.ico", "src/assets", "src/manifest.json"]}}}}, "cashless-e2e": {"root": "e2e/", "projectType": "application", "prefix": "", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "cashless:serve"}, "configurations": {"production": {"devServerTarget": "cashless:serve:production"}}}}}, "cashless-core": {"root": "projects/cashless-core", "sourceRoot": "projects/cashless-core/src", "projectType": "library", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"tsConfig": "projects/cashless-core/tsconfig.lib.json", "project": "projects/cashless-core/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/cashless-core/tsconfig.lib.prod.json"}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/cashless-core/src/test.ts", "tsConfig": "projects/cashless-core/tsconfig.spec.json", "karmaConfig": "projects/cashless-core/karma.conf.js"}}}}}, "cli": {"analytics": false}}