import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {
  BaseComponent,
  EventPickerData,
  FamilyDayOrders,
  MenuPickerData,
  MenuTypeEnum,
  SchoolEvent,
  SchoolEventsRequest,
  SchoolEventsResponse,
  UserCashless,
} from 'src/app/sharedModels';
import { SchoolEventApiService, SchoolEventManagerService } from 'src/app/sharedServices';
import { CreateOrderService } from 'src/app/sharedServices/order/create-order.service';
import { DateHasPassed } from 'src/app/utility';

@Component({
  selector: 'walk-up-order-filters',
  templateUrl: './walk-up-order-filters.component.html',
  styleUrls: ['./walk-up-order-filters.component.scss'],
})
export class WalkUpOrderFiltersComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() userSelected: UserCashless;
  @Input() merchantId: number = null;
  @Input() isAdmin: boolean;
  @Input() isCanteenMerchant: boolean = true;
  @Output() createOrderEvent = new EventEmitter();
  selectedCanteenMenuType: string = MenuTypeEnum.Recess;
  eventOrderDayDetail: FamilyDayOrders = new FamilyDayOrders();
  canteenOrderDayDetail: FamilyDayOrders = new FamilyDayOrders();
  schoolEventList: SchoolEvent[] = [];

  constructor(
    public dialog: MatDialog,
    private schoolEventManagerService: SchoolEventManagerService,
    private eventApiService: SchoolEventApiService,
    private createOrderService: CreateOrderService
  ) {
    super();
  }

  //TODO: add loading symbol
  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.merchantId?.currentValue) {
      this.loadEventsForMerchant();
    }
    if (changes.userSelected?.currentValue && this.isAdmin) {
      this.loadEventsForAdmin();
    }
  }

  loadEventsForAdmin() {
    this.eventApiService
      .GetEventsBySchoolAPI(this.userSelected.SchoolId, this.userSelected.UserId)
      .subscribe({
        next: (res: SchoolEventsResponse) => {
          this.schoolEventList = this.filterOutPastSchoolEvents(res.Events);
        },
        error: error => {
          this.handleErrorFromService(error);
        },
      });
  }

  loadEventsForMerchant(): void {
    let request: SchoolEventsRequest = {
      SchoolId: this.userSelected.SchoolId,
      StudentId: this.userSelected.UserId,
      MerchantId: this.merchantId,
    };

    this.schoolEventManagerService.GetEventsBySchoolMerchantStudent(request).subscribe({
      next: (res: SchoolEvent[]) => {
        this.schoolEventList = this.filterOutPastSchoolEvents(res);
      },
      error: error => {
        this.handleErrorFromService(error);
      },
    });
  }

  filterOutPastSchoolEvents(eventList: SchoolEvent[]) {
    if (!eventList || eventList?.length === 0) {
      return [];
    }
    return eventList.filter(event => !DateHasPassed(event.EventDate));
  }

  onEventPickerChange(eventData: EventPickerData): void {
    this.eventOrderDayDetail = this.createOrderService.getDayDetail(
      eventData.MenuType,
      eventData.MenuName,
      eventData.MenuId,
      eventData.Date,
      eventData.CutOffDate
    );
  }

  onMenuPickerChange(canteenOrderData: MenuPickerData): void {
    this.selectedCanteenMenuType = canteenOrderData.menuType;
    this.canteenOrderDayDetail = this.createOrderService.getDayDetail(
      canteenOrderData.menuType,
      canteenOrderData.menuName,
      null, // merchant/admin no cut off time
      new Date(),
      null
    );
  }

  CreateOrder(isCanteenOrder: boolean) {
    debugger;
    this.createOrderEvent.emit();
    const dayDetail = isCanteenOrder ? this.canteenOrderDayDetail : this.eventOrderDayDetail;
    this.createOrderService.setDayDetail(dayDetail, this.userSelected);
    this.createOrderService.adminMerchantCreateOrder(
      this.isAdmin,
      this.userSelected,
      this.userSelected.Parents[0]
    );
  }
}
