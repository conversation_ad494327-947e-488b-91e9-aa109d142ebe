<!-- Read-only overlay for entire student view -->
<div *ngIf="this.viewType === 'student'" class="readonly-overlay"></div>

<!-- Read-only banner for student view -->
<!-- <div *ngIf="this.viewType === 'student'" class="readonly-banner">
  <div class="readonly-banner-content">
    <mat-icon>visibility</mat-icon>
    <span class="readonly-text">READ-ONLY VIEW</span>
    <span class="readonly-description">You are observing the merchant's actions. This screen is for viewing only.</span>
  </div>
</div> -->

<div class="top-header" *ngIf="this.viewType == 'student'">
  <div class="logo-container">
    <img [src]="getLogoImageSrc()" alt="Spriggy Schools Logo" (error)="onLogoImageError()">
  </div>

  <!-- Cross-tab sync status -->
  <!-- <div class="sync-status-container">
    <div class="sync-indicator" [class.connected]="isConnectedToOtherTabs()" [class.disconnected]="!isConnectedToOtherTabs()">
      <mat-icon>{{ isConnectedToOtherTabs() ? 'sync' : 'sync_disabled' }}</mat-icon>
      <span class="sync-text">
        {{ isConnectedToOtherTabs() ? 'Synced with Merchant' : 'Not Connected' }}
      </span>
      <small *ngIf="lastSyncTime()" class="last-sync">
        Last sync: {{ lastSyncTime() | date:'HH:mm:ss' }}
      </small>
    </div>
  </div> -->

  <!-- <div class="search-container">
      <mat-form-field appearance="outline">
        <input matInput placeholder="Search Student" (input)="onSearchInput($event)">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
    </div> -->
</div>
<div class="pos-tab-container" [class.readonly-mode]="this.viewType === 'student'">


  <br>
  <br>
  <!-- Filters Section -->
  <!-- Loading State -->


  <div class="row">
    <div class="col-12 col-md-8 col-lg-8 col-xl-7 offset-xl-1">
      <!-- Cross-tab sync status for merchant -->
      <!-- <div class="merchant-sync-status mb-3" *ngIf="isConnectedToOtherTabs()">
        <div class="alert alert-info d-flex align-items-center">
          <mat-icon class="me-2">sync</mat-icon>
          <span>Connected to Student View</span>
          <small *ngIf="lastSyncTime()" class="ms-auto">
            Last sync: {{ lastSyncTime() | date:'HH:mm:ss' }}
          </small>
        </div>
      </div> -->

      <mat-card class="filters-card" *ngIf="this.viewType == 'merchant'">
        <mat-card-content>
          <form [formGroup]="orderingForm" class="ordering-form">
            <div class="row">
              <!-- Student Selection -->
              <div class="col-12 col-md-4 mb-3">
                <student-search-dropdown #studentSearchDropdown placeholder="Search for a student..." [schoolId]="schoolId"
                  (studentSelected)="onStudentSelected($event)"></student-search-dropdown>
              </div>

              <!-- Menu Type Selection -->
              <div class="col-12 col-md-4 mb-3">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Ordering Type</mat-label>
                  <mat-select formControlName="menuType">
                    <mat-option *ngFor="let menuOption of menuPickerData" [value]="menuOption.menuType">
                      {{ menuOption.menuName }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <!-- Date Selection -->
              <div class="col-12 col-md-4 mb-3">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Order Date</mat-label>
                  <mat-select formControlName="orderDate">
                    <mat-option *ngFor="let day of listDays" [value]="day">
                      {{ day | date : 'EEEE d MMM' }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
          </form>
          <!-- Favorite Color Confirmation -->
              <div *ngIf="showColorConfirmation() && !IsGuest" class="col-12 mb-3">
                <div class="color-confirmation-card">
                  <div class="color-confirmation-header">
                    <mat-icon>security</mat-icon>
                    <h6>Ask the child's favourite colour to confirm their identity</h6>
                  </div>
                  <div class="color-confirmation-content">
                    <div class="color-circle" [style.background-color]="studentFavoriteColor()"></div>
                    <!-- <div class="color-confirmation-actions">
                      <button mat-raised-button color="primary" (click)="confirmColorIdentity()" class="confirm-btn">
                        <mat-icon>check</mat-icon>
                        Confirmed
                      </button>
                      <button mat-raised-button color="warn" (click)="cancelColorConfirmation()" class="cancel-btn">
                        <mat-icon>close</mat-icon>
                        Cancel
                      </button>
                    </div> -->
                  </div>
                </div>
              </div>
        </mat-card-content>
      </mat-card>
      <div
        *ngIf="menuLoading() && (this.viewType === 'merchant' || (this.viewType === 'student' && showMenuInStudentView())); else result"
        class="col-12 d-flex justify-content-center pt-4">
        <app-spinner [manual]="true"></app-spinner>
      </div>
      <!-- Results Section -->
      <ng-template #result>
        <!-- Student view waiting message -->
        <div *ngIf="this.viewType === 'student' && !showMenuInStudentView()" class="row">
          <div class="col-12 col-md-8 col-lg-8 col-xl-7 offset-xl-1">
            <mat-card class="waiting-card">
              <mat-card-content class="text-center">
                <mat-icon class="waiting-icon">person_search</mat-icon>
                <h4 class="waiting-title">Waiting for Student Selection</h4>
                <p class="waiting-message">
                  Please select a student from the merchant view to begin ordering.
                </p>
                <!-- <div class="sync-status-inline" *ngIf="isConnectedToOtherTabs()">
              <mat-icon class="sync-icon">sync</mat-icon>
              <span>Connected to Merchant View</span>
            </div> -->
              </mat-card-content>
            </mat-card>
          </div>
        </div>

        <!-- Show content only if merchant view OR student view with selected student -->
        <div *ngIf="this.viewType === 'merchant' || (this.viewType === 'student' && showMenuInStudentView())">
          <!-- No Menu Message -->
          <div *ngIf="noMenuMessage; else menu">
            <div class="row">
              <div class="col-12 col-md-8 col-lg-8 col-xl-7 offset-xl-1">
                <mat-card class="no-menu-card">
                  <mat-card-content>
                    <p class="no-menu-message">{{ noMenuMessage }}</p>
                  </mat-card-content>
                </mat-card>
              </div>
            </div>
          </div>

          <!-- Menu Display -->
          <ng-template #menu>
            <div *ngIf="currentMenu?.MenuJSON" class="row">
              <div class="col-12 col-md-12 col-lg-12 col-xl-12 offset-xl-1 categories-container">
                <!-- Category sync indicator for student view -->
                <!-- <div *ngIf="this.viewType === 'student'" class="category-sync-indicator">
            <mat-icon>sync</mat-icon>
            <span>Categories sync with merchant selection</span>
          </div> -->

                <ul class="categories-list scrolling-horizontal-wrapper">
                  <li *ngFor="let cat of currentMenu.MenuJSON; index as i" id="menu-category-{{ i }}"
                    class="category-item" trackBy:cat.CategoryId>
                    <category-tile (click)="this.viewType === 'merchant' ? SetCategory(cat) : null" [name]="cat.CatName"
                      [iconName]="cat.CatUrl" [isSelected]="IsCurrentCategory(cat)"
                      [class.readonly-category]="this.viewType === 'student'">
                    </category-tile>
                  </li>
                </ul>
              </div>
            </div>

            <ng-container *ngIf="currentCategoryToDisplay">
              <div class="row">
                <div class="col-12 col-md-12 col-lg-12 col-xl-12 offset-xl-1" style="margin-left: 0px;">
                  <h4 class="category-title">{{ currentCategoryToDisplay.CatName }}</h4>
                  <div class="row">
                    <div class="col-12 col-lg-6 mb-3" *ngFor="let item of currentCategoryToDisplay.item; index as i">
                      <mat-card appearance="outlined" class="menu-item-card">
                        <product-item [category]="currentCategoryToDisplay" [item]="item"
                          [dateOrder]="selectedOrderDate" [currentMenuType]="selectedMenuType"
                          [schoolCutOffTime]="menuCutOffTime" (clickItem)="AddToCart($event)"
                          (itemDialogOpened)="onItemDialogOpened($event)"
                          (itemDialogClosed)="onItemDialogClosed($event)" [id]="'pos-product-item-' + i">
                        </product-item>
                      </mat-card>
                    </div>

                    <!-- No Items Message -->
                    <div *ngIf="currentCategoryToDisplay.item && currentCategoryToDisplay.item.length === 0"
                      class="col-12">
                      <mat-card class="no-items-card">
                        <mat-card-content>
                          <p class="no-items-message">No items available in this category</p>
                        </mat-card-content>
                      </mat-card>
                    </div>
                  </div>
                </div>


              </div>
            </ng-container>
          </ng-template>
        </div> <!-- Close conditional div for merchant/student with selected student -->
      </ng-template>


    </div>
    <!-- Shopping Cart Sidebar -->
    <div class="col-12 col-md-4 col-lg-4 col-xl-3">
      <div class="shopping-cart-sidebar">
        <mat-card class="cart-card">
          <mat-card-header>
            <mat-card-title>
              Shopping Cart
              <!-- <span *ngIf="this.viewType === 'student'" class="cart-sync-indicator">
                      <mat-icon [class.syncing]="cartSyncInProgress()">sync</mat-icon>
                      <span class="sync-text">
                        <span *ngIf="cartSyncInProgress()">Syncing...</span>
                        <span *ngIf="!cartSyncInProgress() && lastCartSyncTime()">
                          Synced {{ lastCartSyncTime() }}
                        </span>
                        <span *ngIf="!cartSyncInProgress() && !lastCartSyncTime()">
                          Synced from Merchant
                        </span>
                      </span>
                    </span> -->
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <!-- Cart sync notification for student view -->
            <div *ngIf="this.viewType === 'student' && cartSyncInProgress()" class="cart-sync-notification">
              <mat-icon>sync</mat-icon>
              <span>Cart updating from merchant...</span>
            </div>

            <div *ngIf="shoppingCart.length === 0" class="empty-cart">
              <p *ngIf="this.viewType === 'merchant'">Your cart is empty</p>
              <p *ngIf="this.viewType === 'student'">Merchant's cart is empty</p>
            </div>

            <!-- Parent Balance Section -->
              <div *ngIf="selectedStudent && !IsGuest" class="balance-section" style="margin-bottom: 13px;">
                <div *ngIf="balanceLoading()" class="balance-loading">
                  <mat-icon class="loading-icon">hourglass_empty</mat-icon>
                  <span>Loading balance...</span>
                </div>
                <div *ngIf="!balanceLoading() && parentBalance() !== null" class="balance-display">
                  <mat-icon>account_balance_wallet</mat-icon>
                  <span>Remaining balance in parent wallet: {{ parentBalance() | currency }}</span>
                </div>
                <div *ngIf="!balanceLoading() && balanceError()" class="balance-error">
                  <mat-icon>error</mat-icon>
                  <span>{{ balanceError() }}</span>
                </div>
              </div>

            <div *ngIf="shoppingCart.length > 0" class="cart-items">
              <div *ngFor="let item of shoppingCart" class="cart-item">
                <div class="item-info">
                  <h6 class="item-name">{{ item.name }}</h6>
                  <p class="item-details">
                    <span *ngFor="let option of item.selectedOptions">
                      {{ option.optionName }}
                    </span>
                  </p>
                </div>
                <div class="item-actions">
                  <span class="item-price">{{ item.itemPriceIncGst | currency }}</span>
                  <span class="item-quantity" *ngIf="item.quantity > 1">x{{ item.quantity }}</span>
                  <button *ngIf="this.viewType === 'merchant'" mat-icon-button
                    (click)="removeFromCart(item.itemCartId)">
                    <mat-icon>delete</mat-icon>
                  </button>
                  <span *ngIf="this.viewType === 'student'" class="readonly-indicator">
                    <mat-icon>visibility</mat-icon>
                  </span>
                </div>
              </div>

              <div class="cart-total">
                <h5>Total: {{ priceCart() | currency }}</h5>
              </div>

              
            </div>
          </mat-card-content>
          <mat-card-actions *ngIf="shoppingCart.length > 0" class="payment-section">
            <!-- Payment Methods Section (Merchant Only) -->
            <!-- <div  class="payment-methods-container">
                    <mat-card-title style="font-size:17px" class="mat-mdc-card-title">Payment</mat-card-title>

                   
                    <button
                      mat-raised-button
                      [class]="selectedPaymentMethod() === 'spriggy' ? 'payment-btn primary-payment selected' : 'payment-btn primary-payment'"
                      (click)="selectPaymentMethod('spriggy')">
                      <mat-icon>credit_card</mat-icon>
                      <span>Pay with Spriggy Card / Wallet ({{ priceCart() | currency }})</span>
                    </button>

                  
                    <div class="secondary-payment-row">
                      <button
                        mat-stroked-button
                        [class]="selectedPaymentMethod() === 'stripe' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'"
                        data-payment="stripe"
                        (click)="selectPaymentMethod('stripe')"
                        [disabled]="false">
                        <mat-icon>payment</mat-icon>
                        <span>Pay with Stripe</span>
                      </button>

                      <button
                        mat-stroked-button
                        [class]="selectedPaymentMethod() === 'cash' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'"
                        data-payment="cash"
                        (click)="selectPaymentMethod('cash')"
                        [disabled]="false">
                        <mat-icon>money</mat-icon>
                        <span>Pay Cash</span>
                      </button>
                    </div>

                   
                    <div class="secondary-payment-row">
                      <button
                        mat-stroked-button
                        [class]="selectedPaymentMethod() === 'applepay' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'"
                        data-payment="applepay"
                        (click)="selectPaymentMethod('applepay')"
                        [disabled]="false">
                        <mat-icon>phone_iphone</mat-icon>
                        <span>Apple Pay</span>
                      </button>

                      <button
                        mat-stroked-button
                        [class]="selectedPaymentMethod() === 'visa' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'"
                        data-payment="visa"
                        (click)="selectPaymentMethod('visa')"
                        [disabled]="false">
                        <mat-icon>credit_card</mat-icon>
                        <span>Visa</span>
                      </button>
                    </div>

                  
                    <button
                      mat-raised-button
                      class="final-place-order-btn"
                      (click)="OrderClick()">
                      Place Order ({{ priceCart() | currency }})
                    </button>
                  </div> -->
            <!-- Payment Container -->
            <div class="payment-methods-container">
              <mat-card-title style="font-size:17px" class="mat-mdc-card-title">Payment</mat-card-title>

              <!-- Primary Payment -->
              <div class="row" *ngIf="!IsGuest">
                <div class="col-12">
                  <button mat-raised-button
                    [class]="selectedPaymentMethod() === 'spriggy' ? 'payment-btn primary-payment selected' : 'payment-btn primary-payment'"
                    (click)="selectPaymentMethod('spriggy')">
                    <mat-icon>credit_card</mat-icon>
                    <span>Pay with Spriggy Card ({{ priceCart() | currency }})</span>
                  </button>
                </div>
              </div>

              <!-- Secondary Payment Options Row 1 -->
              <div class="row">
                <div class="col-lg-6 col-md-12">
                  <button mat-stroked-button
                    [class]="selectedPaymentMethod() === 'stripe' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'"
                    data-payment="stripe" (click)="selectPaymentMethod('stripe')">
                    <mat-icon>payment</mat-icon>
                    <span>Pay with Stripe</span>
                  </button>
                </div>
                <div class="col-lg-6 col-md-12" style="padding-left: 0px;">
                  <button mat-stroked-button
                    [class]="selectedPaymentMethod() === 'cash' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'"
                    data-payment="cash" (click)="selectPaymentMethod('cash')">
                    <mat-icon>money</mat-icon>
                    <span>Pay Cash</span>
                  </button>
                </div>
              </div>

              <!-- Secondary Payment Options Row 2 -->
              <div class="row">
                <div class="col-lg-6 col-md-12">
                  <button mat-stroked-button
                    [class]="selectedPaymentMethod() === 'applepay' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'"
                    data-payment="applepay" (click)="selectPaymentMethod('applepay')">
                    <mat-icon>phone_iphone</mat-icon>
                    <span>Apple Pay</span>
                  </button>
                </div>
                <div class="col-lg-6 col-md-12" style="padding-left: 0px;">
                  <button mat-stroked-button
                    [class]="selectedPaymentMethod() === 'visa' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'"
                    data-payment="visa" (click)="selectPaymentMethod('visa')">
                    <mat-icon>credit_card</mat-icon>
                    <span>Visa</span>
                  </button>
                </div>
              </div>

              <!-- Final Place Order Button -->
              <div class="row">
                <div class="col-12">
                  <button mat-raised-button class="final-place-order-btn" (click)="OrderClick()">
                    Place Order ({{ priceCart() | currency }})
                  </button>
                </div>
              </div>

              <div class="row">
                <div class="col-12">
                  <div *ngIf="this.viewType === 'student'" class="readonly-order-info">
                    <mat-icon>visibility</mat-icon>
                    <span>Merchant will place order: {{ priceCart() | currency }}</span>
                  </div>
                </div>
              </div>
            </div>

          </mat-card-actions>
        </mat-card>
      </div>
    </div>
  </div>



  <!-- Mobile Balance Display -->
  <div *ngIf="selectedStudent && parentBalance() !== null && this.viewType === 'merchant'" class="mobile-balance-display d-block d-md-none">
    <div class="mobile-balance-content">
      <mat-icon>account_balance_wallet</mat-icon>
      <span>Remaining balance: {{ parentBalance() | currency }}</span>
    </div>
  </div>

  <!-- Mobile Place Order Button (Merchant Only) -->
  <div *ngIf="showMobilePlaceOrder && this.viewType === 'merchant'" class="mobile-place-order d-block d-md-none">
    <div class="mobile-payment-container">
      <!-- Selected Payment Method Indicator (Clickable) -->
      <div class="mobile-payment-indicator" (click)="openMobilePaymentSelector()">
        <mat-icon>credit_card</mat-icon>
        <span style="font-size: 0.9rem!important;">Pay with Spriggy Card</span>
        <mat-icon class="expand-icon">expand_less</mat-icon>
      </div>

      <!-- Place Order FAB -->
      <button mat-fab color="primary" (click)="OrderClick()" class="fab-place-order">
        <mat-icon>shopping_cart</mat-icon>
      </button>
    </div>
  </div>

  <!-- Mobile Payment Method Selector -->
  <div *ngIf="showMobilePaymentSelector() && this.viewType === 'merchant'"
    class="mobile-payment-selector-overlay d-block d-md-none">
    <div class="mobile-payment-selector">
      <div class="mobile-payment-header">
        <h6>Select Payment Method</h6>
        <button mat-icon-button (click)="closeMobilePaymentSelector()">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <div class="mobile-payment-options">
        <button mat-stroked-button
          [class]="selectedPaymentMethod() === 'spriggy' ? 'mobile-payment-option selected primary' : 'mobile-payment-option primary'"
          (click)="selectPaymentMethod('spriggy')">
          <mat-icon>credit_card</mat-icon>
          <span>Pay with Spriggy Card / Wallet</span>
          <mat-icon *ngIf="selectedPaymentMethod() === 'spriggy'" class="check-icon">check_circle</mat-icon>
        </button>


        <button mat-stroked-button class="mobile-payment-option" [disabled]="true"
          (click)="selectPaymentMethod('stripe')">
          <mat-icon>payment</mat-icon>
          <span>Pay with Stripe</span>
        </button>

        <button mat-stroked-button class="mobile-payment-option" [disabled]="true"
          (click)="selectPaymentMethod('cash')">
          <mat-icon>money</mat-icon>
          <span>Pay Cash</span>
        </button>

        <button mat-stroked-button class="mobile-payment-option" [disabled]="true"
          (click)="selectPaymentMethod('applepay')">
          <mat-icon>phone_iphone</mat-icon>
          <span>Apple Pay</span>
        </button>

        <button mat-stroked-button class="mobile-payment-option" [disabled]="true"
          (click)="selectPaymentMethod('visa')">
          <mat-icon>credit_card</mat-icon>
          <span>Visa</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Read-only Indicator (Student Only) -->
  <div *ngIf="showMobilePlaceOrder && this.viewType === 'student' && showMenuInStudentView()"
    class="mobile-readonly-indicator d-block d-md-none">
    <div class="readonly-fab">
      <mat-icon>visibility</mat-icon>
      <span class="cart-total">{{ priceCart() | currency }}</span>
    </div>
  </div>

  <!-- Student View Popup (Read-only) -->
  <div *ngIf="showStudentPopup() && this.viewType === 'student'" class="student-popup-overlay">
    <div class="student-popup-content">
      <div class="popup-header">
        <!-- <span class="merchant-indicator">Merchant View</span> -->
        <h3>Item Selected</h3>
      </div>

      <div *ngIf="studentPopupData()" class="popup-body">
        <img *ngIf="studentPopupData().item.Images && studentPopupData().item.Images.length > 0"
          [src]="getItemImageUrl(studentPopupData().item.Images[0].ImageUrl)" [alt]="studentPopupData().item.Name"
          class="popup-item-image" (error)="$event.target.src = defaultImagePath" />

        <div class="popup-item-details">
          <div class="item-name">{{ studentPopupData().item.Name }}</div>
          <div class="item-price">${{ studentPopupData().item.Price | number:'1.2-2' }}</div>
          <div *ngIf="studentPopupData().item.Description" class="item-description">
            {{ studentPopupData().item.Description }}
          </div>
        </div>

        <!-- <div class="readonly-notice">
          <mat-icon style="vertical-align: middle; margin-right: 0.5rem;">visibility</mat-icon>
          This is a read-only view. The merchant is selecting this item.
        </div> -->
      </div>
    </div>
  </div>